"""logwp.extras.petroplot.crossplot.internal.plotter - 交会图内部绘图引擎

本模块包含生成二维交会图的核心绘图逻辑。

Architecture
------------
层次/依赖: petroplot/crossplot内部实现层，被facade层调用
设计原则: 关注点分离、配置驱动、无副作用
"""

from typing import Optional, Dict, Any, List, Tuple
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from logwp.infra import get_logger

from logwp.extras.petroplot.common import LayoutManager, LegendManager
from logwp.extras.petroplot.common.config import ContinuousColorConfig, CategoricalColorConfig
from logwp.extras.petroplot.common.plotter_abc import CartesianPlotter as AbstractCartesianPlotter
from ..config import CrossPlotConfig, CrossPlotColumnSelectors, SeriesColumnSelectors
from .marginal_plot_helper import MarginalPlotHelper
from logwp.extras.plotting import PlotProfile

logger = get_logger(__name__)

class CrossPlotter(AbstractCartesianPlotter):
    """
    一个专门用于绘制二维交会图的绘图器。

    它继承自通用的 CartesianPlotter 抽象基类，并实现了
    绘制交会图散点和坐标轴所需的特定逻辑。
    """

    def __init__(
        self,
        config: CrossPlotConfig,
        selectors: CrossPlotColumnSelectors,
        plot_profile: PlotProfile,
        data_dict: Dict[str, pd.DataFrame],
        subtitle: Optional[str] = None,
    ):
        super().__init__(config, selectors, plot_profile, data_dict, subtitle)
        self._marginal_helper = MarginalPlotHelper(
            config=self.config,
            plot_profile=self.plot_profile,
            data_dict=self.data_dict,
            selectors=self.selectors
        )

    def _setup_figure(self) -> None:
        """
        根据配置初始化Figure对象。
        此方法将Figure的创建委托给 MarginalPlotHelper，后者会根据配置
        决定是创建标准Figure还是带子图的复杂布局。
        """
        self.fig = self._marginal_helper.setup_figure()

    def _add_data_traces(self) -> None:
        """
        根据多系列配置添加所有数据轨迹。
        此方法迭代 self.selectors.series，并为每个系列绘制一个或多个轨迹。
        """
        for series_selector in self.selectors.series:
            df_original = self.data_dict.get(series_selector.bundle_name)
            if df_original is None or df_original.empty:
                logger.warning(f"系列 '{series_selector.name}' 的数据源 '{series_selector.bundle_name}' 未找到或为空，将跳过绘制。")
                continue

            # --- 【新增】前置列存在性检查，防止KeyError ---
            required_cols = [
                c for c in [
                    series_selector.x_col,
                    series_selector.y_col,
                    series_selector.color_col,
                    series_selector.symbol_col,
                    series_selector.well_col,
                    series_selector.depth_col,
                ] if c
            ]
            if series_selector.hover_extra_cols:
                required_cols.extend(series_selector.hover_extra_cols)

            missing_cols = [c for c in required_cols if c not in df_original.columns]
            if missing_cols:
                logger.warning(
                    f"系列 '{series_selector.name}' 的数据源 '{series_selector.bundle_name}' "
                    f"缺少以下必需的列: {missing_cols}，将跳过绘制。"
                )
                continue

            # 【新增逻辑】在绘图前，移除x或y坐标为空的行
            required_coord_cols = [col for col in [series_selector.x_col, series_selector.y_col] if col]
            if not required_coord_cols:
                logger.warning(f"系列 '{series_selector.name}' 未指定任何坐标轴列 (x_col, y_col)，将跳过绘制。")
                continue

            df = df_original.dropna(subset=required_coord_cols)

            if df.empty:
                logger.warning(f"系列 '{series_selector.name}' 在移除x/y坐标的空值后数据为空，将跳过绘制。")
                continue

            if series_selector.plot_type == "scatter":
                self._add_scatter_trace(series_selector, df)
            elif series_selector.plot_type == "line":
                self._add_line_trace(series_selector, df)

        # 在所有主轨迹绘制完毕后，统一绘制边缘分布图
        self._marginal_helper.draw_marginal_plots(self.fig)

    def _add_scatter_trace(self, selector: SeriesColumnSelectors, df: pd.DataFrame):
        """为单个散点图系列添加轨迹。"""
        # 此处可以复用基类的部分逻辑，但为了清晰，我们在此重写
        # 核心逻辑：区分空值点和非空值点
        data_with_color = df
        data_without_color = pd.DataFrame()

        if selector.color_col and selector.distinguish_null_color:
            notna_mask = df[selector.color_col].notna()
            data_with_color = df[notna_mask]
            data_without_color = df[~notna_mask]

        # 1. 绘制空值点
        if not data_without_color.empty:
            null_style = selector.null_marker_style
            profile_marker_style = self.plot_profile.artist_props.get("scatter", {})
            self.fig.add_trace(go.Scatter(
                x=data_without_color[selector.x_col],
                y=data_without_color[selector.y_col],
                mode='markers',
                marker={
                    'color': null_style.color,
                    'size': profile_marker_style.get("size", 8) * null_style.size_factor,
                    'symbol': null_style.symbol
                },
                name=f"{selector.name} ({null_style.label})",
                legendgroup=selector.name,
                hoverinfo='skip',
            ), **self._marginal_helper.get_main_subplot_args())

        # 2. 绘制有值点
        if not data_with_color.empty:
            scatter_args, add_trace_args = self._build_base_trace_args(selector, data_with_color)

            profile_marker_style = self.plot_profile.artist_props.get("scatter", {})
            marker_props = {
                'size': profile_marker_style.get("size", 8),
                'line': profile_marker_style.get("line", {"width": 0.5, "color": "white"}),
            }

            # --- Color Mapping ---
            if selector.color_col and selector.color_mapping:
                color_map_cfg = selector.color_mapping
                if isinstance(color_map_cfg, ContinuousColorConfig):
                    # 新策略：不再在Trace级别定义颜色轴布局。
                    # 只定义颜色数据，并将其链接到全局的 'coloraxis'。
                    marker_props.update({
                        'color': data_with_color[selector.color_col],
                        'colorscale': color_map_cfg.colorscale,
                        'reversescale': color_map_cfg.reverse_scale,
                        'cmin': color_map_cfg.cmin,
                        'cmax': color_map_cfg.cmax,
                        'showscale': False,
                        'coloraxis': 'coloraxis'
                    })
                elif isinstance(color_map_cfg, CategoricalColorConfig) and color_map_cfg.color_map:
                    marker_props['color'] = data_with_color[selector.color_col].map(color_map_cfg.color_map)

            # --- Symbol Mapping ---
            if selector.symbol_col and selector.symbol_mapping and selector.symbol_mapping.symbol_map:
                marker_props['symbol'] = data_with_color[selector.symbol_col].map(selector.symbol_mapping.symbol_map)

            scatter_args['mode'] = 'markers'
            scatter_args['marker'] = marker_props
            self.fig.add_trace(go.Scatter(**scatter_args), **add_trace_args)

    def _add_line_trace(self, selector: SeriesColumnSelectors, df: pd.DataFrame):
       """为单个折线图系列添加轨迹。"""
       scatter_args, add_trace_args = self._build_base_trace_args(selector, df)

       # 【重构】更健壮的样式合并逻辑
       if selector.line_style:
           # 如果用户为本系列提供了特定的LineStyle，则使用它
           line_props = selector.line_style.model_dump(exclude_none=True)
       else:
           # 否则，回退到PlotProfile中为"line"定义的全局默认样式
           line_props = self.plot_profile.artist_props.get("line", {})

       scatter_args['mode'] = 'lines'
       scatter_args['line'] = line_props
       self.fig.add_trace(go.Scatter(**scatter_args), **add_trace_args)

    def _build_base_trace_args(self, selector: SeriesColumnSelectors, df: pd.DataFrame) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        构建用于创建go.Scatter轨迹的通用参数字典。

        Returns:
            一个元组，包含:
            - scatter_args: 用于 go.Scatter() 构造函数的参数字典。
            - add_trace_args: 用于 fig.add_trace() 的参数字典 (例如, row, col)。
        """
        hovertemplate, custom_data_cols = self._build_series_hover_template(selector, df, self.config)
        customdata = df[custom_data_cols].values if custom_data_cols else None

        scatter_args = {
            "x": df[selector.x_col] if selector.x_col else None,
            "y": df[selector.y_col] if selector.y_col else None,
            "name": selector.name, "legendgroup": selector.name,
            "customdata": customdata, "hovertemplate": hovertemplate,
        }
        add_trace_args = self._marginal_helper.get_main_subplot_args()
        return scatter_args, add_trace_args

    def _build_series_hover_template(self, selector: SeriesColumnSelectors, df: pd.DataFrame, config: CrossPlotConfig) -> Tuple[str, List[str]]:
        """为单个系列构建悬停模板和 custom_data 列列表。"""
        custom_data_cols = []
        template_parts = []

        def add_to_hover(col_name: Optional[str], label: str, is_numeric: bool = False):
            """局部辅助函数，用于封装向悬停信息中添加一个字段的逻辑。"""
            if not col_name or col_name not in df.columns:
                return
            custom_data_cols.append(col_name)
            format_code = ":.2f" if is_numeric else ""
            template_parts.append(f'<b>{label}</b>: %{{customdata[{len(custom_data_cols)-1}]{format_code}}}')

        # --- 1. 添加主坐标信息 (不使用 customdata) ---
        if selector.x_col:
            x_label = config.x_axis.label or selector.x_col
            template_parts.append(f'<b>{x_label}</b>: %{{x:.2f}}')
        if selector.y_col:
            y_label = config.y_axis.label or selector.y_col
            template_parts.append(f'<b>{y_label}</b>: %{{y:.2f}}')

        # --- 2. 添加标准的自定义数据 ---
        add_to_hover(selector.well_col, "Well")
        add_to_hover(selector.depth_col, "Depth", is_numeric=True)

        # --- 3. 添加颜色和符号信息 ---
        if selector.color_col and selector.color_mapping:
            if isinstance(selector.color_mapping, CategoricalColorConfig):
                add_to_hover(selector.color_col, selector.color_col)
            elif isinstance(selector.color_mapping, ContinuousColorConfig):
                template_parts.append(f'<b>{selector.color_col}</b>: %{{marker.color:.2f}}')

        add_to_hover(selector.symbol_col, selector.symbol_col if selector.symbol_col else "")

        # --- 4. 添加额外的悬停列 ---
        if selector.hover_extra_cols:
            for col in selector.hover_extra_cols:
                is_numeric = pd.api.types.is_numeric_dtype(df[col]) if col in df.columns else False
                add_to_hover(col, col, is_numeric)

        # --- 5. 组装最终模板 ---
        template_parts.append('<extra></extra>')
        hovertemplate = "<br>".join(template_parts)

        # 使用 dict.fromkeys 移除可能因配置重叠而产生的重复列，同时保持顺序
        return hovertemplate, list(dict.fromkeys(custom_data_cols))

    def _translate_font_config(self) -> Optional[Dict[str, Any]]:
        """将PlotProfile的字体配置翻译为plotly格式。"""
        font_config = {}

        # 从rc_params获取全局字体设置
        rc_params = self.plot_profile.rc_params
        if "font.family" in rc_params:
            font_config["family"] = rc_params["font.family"]
        if "font.size" in rc_params:
            font_config["size"] = rc_params["font.size"]

        # 从figure_props获取颜色设置
        if "figure.facecolor" in rc_params:
            # 这里不设置字体颜色，因为facecolor是背景色
            pass

        # 如果没有字体配置，返回默认配置
        if not font_config:
            font_config = {"family": "Arial", "size": 12}

        return font_config if font_config else None

    def _sync_marginal_axes_with_main(self) -> None:
        """确保边缘图的坐标轴范围和类型与主图同步。"""
        if not self._marginal_helper.is_active:
            return

        # 获取主图的坐标轴配置
        main_xaxis = self.fig.layout.xaxis
        main_yaxis = self.fig.layout.yaxis2  # 主图的Y轴是yaxis2

        # 同步X轴边缘图的X轴 (与主图X轴相同)
        if main_xaxis and hasattr(main_xaxis, 'type'):
            self.fig.update_xaxes(
                type=main_xaxis.type,
                range=main_xaxis.range,
                row=1, col=1  # X轴边缘图位置
            )

        # 同步Y轴边缘图的Y轴 (与主图Y轴相同)
        if main_yaxis and hasattr(main_yaxis, 'type'):
            self.fig.update_yaxes(
                type=main_yaxis.type,
                range=main_yaxis.range,
                row=2, col=2  # Y轴边缘图位置
            )

    def _apply_layout(self) -> None:
        """应用整体布局和样式，使用新的组合式辅助工具。"""
        # 1. 绘图器自己准备“个性化”的布局片段
        axes_config = self._setup_cartesian_axes()

        # 查找是否需要一个全局的、共享的颜色轴
        continuous_color_cfg = self._find_continuous_color_config()

        # 2. 从“通用”管理器获取“标准”布局零件
        layout_mgr = LayoutManager(self.config, self.plot_profile, self.subtitle)
        layout_updates = layout_mgr.build_base_layout()

        # 绘图器作为“总指挥”，明确设定画布尺寸
        # 【修复】正确翻译PlotProfile的figure_props到plotly尺寸
        figure_props = self.plot_profile.figure_props
        if "figsize" in figure_props:
            # matplotlib的figsize是英寸，转换为像素 (假设DPI=100)
            figsize = figure_props["figsize"]
            layout_updates.update({
                'width': int(figsize[0] * 100),
                'height': int(figsize[1] * 100),
            })
        else:
            # 使用默认尺寸
            layout_updates.update({
                'width': figure_props.get("width", 800),
                'height': figure_props.get("height", 700),
            })

        # 【修复】确保全局字体配置正确应用
        font_config = self._translate_font_config()
        if font_config:
            layout_updates['font'] = font_config

        legend_mgr = LegendManager(self.config, self.plot_profile)
        legend_updates = legend_mgr.build_legend_layout(
            has_standard_legend=self._has_standard_legend_items()
        )

        # 3. 作为“总指挥”，按顺序合并所有布局片段
        layout_updates.update(axes_config)
        layout_updates.update(legend_updates)

        # 如果需要，添加全局颜色轴的定义
        # 【重构】颜色条逻辑：数据映射来自系列配置，布局来自顶层配置
        if continuous_color_cfg and self.config.color_bar and self.config.color_bar.show:
            colorbar_style = self.plot_profile.artist_props.get("colorbar", {})
            colorbar_cfg = self.config.color_bar

            # 找到第一个使用此配置的系列，以获取其列名作为默认标题
            color_col_for_title = "Value"
            for s in self.selectors.series:
                if isinstance(s.color_mapping, ContinuousColorConfig):
                    color_col_for_title = s.color_col
                    break

            layout_updates['coloraxis'] = {
                # --- 数据映射部分 ---
                'colorscale': continuous_color_cfg.colorscale,
                'reversescale': continuous_color_cfg.reverse_scale,
                'cmin': continuous_color_cfg.cmin,
                'cmax': continuous_color_cfg.cmax,
                # --- 布局表现部分 ---
                'colorbar': {
                    'title': {
                        'text': colorbar_cfg.title or color_col_for_title,
                        'side': colorbar_style.get("title_side", "right"),
                        'font': colorbar_style.get("titlefont", {})
                    },
                    **colorbar_cfg.model_dump(exclude={'show', 'title'}),
                    'tickfont': colorbar_style.get("tickfont", {}),
                }
            }

        # 4. 一次性应用所有更新
        self.fig.update_layout(**layout_updates)

        # 【修复】确保边缘图的坐标轴范围与主图同步
        if self._marginal_helper.is_active:
            self._sync_marginal_axes_with_main()

    def _setup_cartesian_axes(self) -> Dict[str, Any]:
        """构建交会图的坐标轴配置。"""

        def get_axis_config(axis_cfg, axis_name: str) -> Dict[str, Any]:
            """辅助函数，从AxisConfig和PlotProfile构建最终的坐标轴字典。"""

            # 1. 确定默认标题
            default_title = ""
            if self.selectors and self.selectors.series:
                # 增强：遍历所有系列，找到第一个定义了该轴列名的系列，并用其作为默认标题
                # 这比只检查第一个系列更健壮。
                for series_selector in self.selectors.series:
                    col_name = getattr(series_selector, f"{axis_name}_col", None)
                    if col_name:
                        default_title = col_name
                        break

            # 2. 构建基础字典
            axis_dict = {
                'title': axis_cfg.label or default_title,
                'range': [axis_cfg.min, axis_cfg.max] if axis_cfg.min is not None and axis_cfg.max is not None else None,
                'type': axis_cfg.scale,
            }

            # 【修复】应用PlotProfile中的坐标轴字体配置 - 使用Plotly 6.0+的新API
            font_family = self.plot_profile.rc_params.get("font.family")
            font_size = self.plot_profile.rc_params.get("font.size")

            if font_family or font_size:
                # 标题字体配置 - 使用新的嵌套结构
                if isinstance(axis_dict['title'], str):
                    axis_dict['title'] = {'text': axis_dict['title']}
                elif axis_dict['title'] is None:
                    axis_dict['title'] = {'text': ''}

                if 'font' not in axis_dict['title']:
                    axis_dict['title']['font'] = {}

                if font_family:
                    axis_dict['title']['font']['family'] = font_family
                if font_size:
                    axis_dict['title']['font']['size'] = font_size + 2  # 标题稍大

                # 刻度字体配置
                if 'tickfont' not in axis_dict:
                    axis_dict['tickfont'] = {}
                if font_family:
                    axis_dict['tickfont']['family'] = font_family
                if font_size:
                    axis_dict['tickfont']['size'] = font_size

            # 4. 应用坐标轴线、刻度线和网格线配置
            # 这是一个完全重构的部分，用以适配新的、结构化的AxisConfig模型。

            # 4a. 应用坐标轴线样式 (axis_line)
            if axis_cfg.axis_line:
                line_props = axis_cfg.axis_line.model_dump(exclude_unset=True)
                if 'show' in line_props:
                    axis_dict['showline'] = line_props['show']
                if 'color' in line_props:
                    axis_dict['linecolor'] = line_props['color']
                if 'width' in line_props:
                    axis_dict['linewidth'] = line_props['width']

            # 4b. 应用主刻度线和主网格线样式 (major_ticks)
            if axis_cfg.major_ticks:
                major_tick_props = axis_cfg.major_ticks.model_dump(exclude_unset=True)

                # 翻译 TickConfig 属性到 Plotly 坐标轴属性
                if 'show' in major_tick_props:
                    axis_dict['ticks'] = 'outside' if major_tick_props['show'] else ''
                if 'showlabels' in major_tick_props:
                    major_tick_props['showticklabels'] = major_tick_props.pop('showlabels')

                for prop in ['dtick', 'nticks', 'ticklen', 'tickwidth', 'tickcolor', 'showticklabels']:
                    if prop in major_tick_props:
                        axis_dict[prop] = major_tick_props[prop]

                # 翻译 grid_line 属性
                if 'grid_line' in major_tick_props and major_tick_props['grid_line']:
                    grid_props = major_tick_props['grid_line']
                    if 'show' in grid_props:
                        axis_dict['showgrid'] = grid_props['show']
                    if 'color' in grid_props:
                        axis_dict['gridcolor'] = grid_props['color']
                    if 'width' in grid_props:
                        axis_dict['gridwidth'] = grid_props['width']
                    if 'dash' in grid_props:
                        axis_dict['griddash'] = grid_props['dash']

            # 4c. 应用次要刻度线和次要网格线样式 (minor_ticks)
            if axis_cfg.minor_ticks:
                minor_tick_props = axis_cfg.minor_ticks.model_dump(exclude_unset=True)
                minor_dict = {}

                # 翻译 TickConfig 属性到 Plotly 次要刻度属性
                # 注意：Plotly的Minor对象没有'visible'属性，只能通过'ticks'属性控制显示
                if 'show' in minor_tick_props:
                    minor_dict['ticks'] = 'outside' if minor_tick_props['show'] else ''

                for prop in ['dtick', 'nticks', 'ticklen', 'tickwidth', 'tickcolor']:
                    if prop in minor_tick_props:
                        minor_dict[prop] = minor_tick_props[prop]

                # 翻译 grid_line 属性
                if 'grid_line' in minor_tick_props and minor_tick_props['grid_line']:
                    grid_props = minor_tick_props['grid_line']
                    for prop, plotly_prop in [('show', 'showgrid'), ('color', 'gridcolor'), ('width', 'gridwidth'), ('dash', 'griddash')]:
                        if prop in grid_props:
                            minor_dict[plotly_prop] = grid_props[prop]

                if minor_dict:
                    axis_dict['minor'] = minor_dict

            # 5. 移除值为None的键，保持Plotly布局的清洁
            return {k: v for k, v in axis_dict.items() if v is not None}

        # --- Main Logic ---
        # 1. 无论布局如何，都先构建主X轴和Y轴的配置
        xaxis_cfg = get_axis_config(self.config.x_axis, 'x')
        yaxis_cfg = get_axis_config(self.config.y_axis, 'y')

        # 确定绘图区背景色，职责在此处，而非通用辅助类
        plot_bgcolor = self.config.plot_bgcolor or self.plot_profile.rc_params.get("axes.facecolor", 'white')

        # 2. 根据是否存在边缘图，决定如何将轴配置映射到最终布局
        if self._marginal_helper.is_active:
            # 【修复】边缘图布局的正确坐标轴映射：
            # 主图 (2,1): xaxis + yaxis2
            # X轴边缘图 (1,1): xaxis + yaxis (需要继承主图的X轴设置)
            # Y轴边缘图 (2,2): xaxis2 + yaxis2 (需要继承主图的Y轴设置)

            # 边缘图的坐标轴需要继承主图的刻度类型，但隐藏标签
            x_marginal_axis_cfg = {
                'type': xaxis_cfg.get('type', 'linear'),  # 继承主图X轴的刻度类型
                'showticklabels': False,
                'title': {'text': ''},
                'showgrid': False,
                'showline': False,
                'range': xaxis_cfg.get('range'),  # 继承主图的范围
            }

            y_marginal_axis_cfg = {
                'type': yaxis_cfg.get('type', 'linear'),  # 继承主图Y轴的刻度类型
                'showticklabels': False,
                'title': {'text': ''},
                'showgrid': False,
                'showline': False,
                'range': yaxis_cfg.get('range'),  # 继承主图的范围
            }

            return {
                # 主图坐标轴 (2,1)
                'xaxis': xaxis_cfg,    # 主图X轴
                'yaxis2': yaxis_cfg,   # 主图Y轴

                # X轴边缘图坐标轴 (1,1)
                'yaxis': x_marginal_axis_cfg,   # X轴边缘图的Y轴

                # Y轴边缘图坐标轴 (2,2)
                'xaxis2': y_marginal_axis_cfg,  # Y轴边缘图的X轴

                'plot_bgcolor': plot_bgcolor,
            }
        else:
            # 对于简单布局，直接映射
            return {
                'xaxis': xaxis_cfg,
                'yaxis': yaxis_cfg,
                'plot_bgcolor': plot_bgcolor,
            }

    def _draw_main_plot(self) -> None:
        """
        绘制图表的背景元素，例如参考对角线。
        网格线由_setup_axes处理。
        """
        if not self.config.diagonal_line or not self.config.diagonal_line.show:
            return

        diag_cfg = self.config.diagonal_line
        x_axis_cfg = self.config.x_axis

        # 确定对角线的绘制范围
        # 优先使用用户在坐标轴配置中指定的范围
        if x_axis_cfg.min is None or x_axis_cfg.max is None:
            logger.warning(
                "无法绘制对角线，因为X轴的范围 (min, max) 未在配置中指定。"
                "请在 CrossPlotConfig.x_axis 中设置 min 和 max。"
            )
            return

        x0 = x_axis_cfg.min
        x1 = x_axis_cfg.max
        y0 = diag_cfg.slope * x0 + diag_cfg.intercept
        y1 = diag_cfg.slope * x1 + diag_cfg.intercept

        # 确定线条样式，用户配置优先于模板
        profile_style = self.plot_profile.artist_props.get("diagonal_line", {})
        line_style = diag_cfg.line_style

        final_style = {
            "color": line_style.color or profile_style.get("color"),
            "width": line_style.width or profile_style.get("width"),
            "dash": line_style.dash or profile_style.get("dash"),
        }

        shape_kwargs = {
            "type": "line", "x0": x0, "y0": y0, "x1": x1, "y1": y1,
            "line": final_style, "layer": "below"
        }
        shape_kwargs.update(self._marginal_helper.get_main_subplot_args())
        self.fig.add_shape(**shape_kwargs)

    def _has_standard_legend_items(self) -> bool:
        """
        检查是否存在需要显示在标准图例中的项。

        标准图例是必需的，如果：
        1. 有多个系列需要区分（例如，多条线或多个散点图组）。
        2. 任何一个系列使用了分类颜色映射。
        3. 任何一个系列使用了分类符号映射。
        """
        if not self.selectors or not self.selectors.series:
            return False

        # 情况1: 有多个系列，需要图例来区分它们
        if len(self.selectors.series) > 1:
            return True

        # 情况2: 只有一个系列，但它内部需要图例 (例如，按岩性分类着色或使用不同符号)
        for s in self.selectors.series:
            # 仅当分类颜色映射存在且有效（color_map非空）时，才认为需要图例
            if (isinstance(s.color_mapping, CategoricalColorConfig) and
                    s.color_col and
                    s.color_mapping.color_map):
                return True

            # 仅当符号映射存在且有效（symbol_map非空）时，才认为需要图例
            if (s.symbol_col and
                    s.symbol_mapping and
                    s.symbol_mapping.symbol_map):
                return True

        return False

    def _find_continuous_color_config(self) -> Optional[ContinuousColorConfig]:
        """
        扫描所有系列选择器，找到第一个使用 ContinuousColorConfig 的配置并返回。
        这个配置将被用来定义整个图表唯一的、共享的颜色轴。
        """
        if not self.selectors or not self.selectors.series:
            return None
        for s in self.selectors.series:
            if isinstance(s.color_mapping, ContinuousColorConfig):
                return s.color_mapping
        return None


def draw_crossplot(
    config: CrossPlotConfig,
    selectors: CrossPlotColumnSelectors,
    data_dict: Dict[str, pd.DataFrame],
    plot_profile: PlotProfile,
    subtitle: Optional[str] = None
) -> go.Figure:
    """【内部绘图逻辑】纯粹的绘图函数，将配置和样式应用于数据以生成Plotly图表。"""
    plotter = CrossPlotter(
        config=config,
        selectors=selectors,
        data_dict=data_dict,
        plot_profile=plot_profile,
        subtitle=subtitle
    )
    return plotter.plot()
