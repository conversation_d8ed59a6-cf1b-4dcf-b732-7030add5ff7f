#!/usr/bin/env python3
"""
简单的crossplot测试脚本
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from logwp.extras.petroplot.crossplot import crossplot_create_publication_ready_perm_config
    print("✓ 成功导入crossplot模块")
except ImportError as e:
    print(f"✗ 导入crossplot模块失败: {e}")
    sys.exit(1)

try:
    # 创建简单的测试配置
    plot_config, plot_profile = crossplot_create_publication_ready_perm_config(
        bundle_name="main",
        x_curve="RD_LWD",
        y_curve="RS_LWD",
        color_curve="K_LABEL",
        x_title="RD_LWD(ohm.m)",
        y_title="RS_LWD(ohm.m)",
        x_range=(0.2, 20),
        y_range=(0.2, 20),
        x_log=True,
        y_log=True,
        show_marginal_x=True,
        show_marginal_y=True,
        show_diagonal_line=True,
        title="测试交会图",
        colorscale="Plasma",
        cmin=-2.0,
        cmax=3.0,
        distinguish_null_color=True,
    )
    print("✓ 成功创建crossplot配置")
    print(f"  - X轴刻度类型: {plot_config.x_axis.scale}")
    print(f"  - Y轴刻度类型: {plot_config.y_axis.scale}")
    print(f"  - 显示X轴边缘图: {plot_config.marginal_x.show if plot_config.marginal_x else False}")
    print(f"  - 显示Y轴边缘图: {plot_config.marginal_y.show if plot_config.marginal_y else False}")
    
except Exception as e:
    print(f"✗ 创建crossplot配置失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("✓ 基础配置测试通过")
